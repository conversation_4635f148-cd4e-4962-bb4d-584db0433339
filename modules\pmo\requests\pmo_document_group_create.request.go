package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMODocumentGroupCreate struct {
	core.BaseValidator
	Tab<PERSON>ey    *string `json:"tab_key"`
	GroupName *string `json:"group_name"`
}

func (r *PMODocumentGroupCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>tr<PERSON>equired(r.<PERSON><PERSON>, "tab_key"))
	r.Must(r.<PERSON>Str<PERSON>equired(r.GroupName, "group_name"))
	r.Must(r.Is<PERSON>trIn(r.<PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.Error()
}
