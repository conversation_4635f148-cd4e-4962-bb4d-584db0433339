package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectDocumentService interface {
	// PMO Document Group methods
	DocumentGroupsCreate(input *dto.PMODocumentGroupCreatePayload) (*models.PMODocumentGroup, core.IError)
	DocumentGroupsUpdate(id string, input *dto.PMODocumentGroupUpdatePayload) (*models.PMODocumentGroup, core.IError)
	DocumentGroupsFind(id string) (*models.PMODocumentGroup, core.IError)
	DocumentGroupsPagination(pageOptions *core.PageOptions, options *dto.PMODocumentGroupPaginationOptions) (*repository.Pagination[models.PMODocumentGroup], core.IError)
	DocumentGroupsDelete(id string) core.IError

	// PMO Document Item methods
	DocumentItemsCreate(input *dto.PMODocumentItemCreatePayload) (*models.PMODocumentItem, core.IError)
	DocumentItemsUpdate(id string, input *dto.PMODocumentItemUpdatePayload) (*models.PMODocumentItem, core.IError)
	DocumentItemsFind(id string) (*models.PMODocumentItem, core.IError)
	DocumentItemsPagination(pageOptions *core.PageOptions, options *dto.PMODocumentItemPaginationOptions) (*repository.Pagination[models.PMODocumentItem], core.IError)
	DocumentItemsDelete(id string) core.IError

	// PMO Document Item Version methods
	DocumentItemVersionsPagination(documentItemID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMODocumentItemVersion], core.IError)
	DocumentItemVersionsFind(versionID string) (*models.PMODocumentItemVersion, core.IError)
}

type pmoProjectDocumentService struct {
	ctx core.IContext
}

func NewPMOProjectDocumentService(ctx core.IContext) IPMOProjectDocumentService {
	return &pmoProjectDocumentService{ctx: ctx}
}

// PMO Document Group methods implementation
func (s pmoProjectDocumentService) DocumentGroupsCreate(input *dto.PMODocumentGroupCreatePayload) (*models.PMODocumentGroup, core.IError) {
	documentGroup := &models.PMODocumentGroup{
		BaseModel:   models.NewBaseModel(),
		ProjectID:   input.ProjectID,
		TabKey:      models.PMOTabKey(input.TabKey),
		GroupName:   input.GroupName,
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.PMODocumentGroup(s.ctx).Create(documentGroup)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.DocumentGroupsFind(documentGroup.ID)
}

func (s pmoProjectDocumentService) DocumentGroupsUpdate(id string, input *dto.PMODocumentGroupUpdatePayload) (*models.PMODocumentGroup, core.IError) {
	documentGroup, ierr := s.DocumentGroupsFind(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.TabKey != "" {
		documentGroup.TabKey = models.PMOTabKey(input.TabKey)
	}
	if input.GroupName != "" {
		documentGroup.GroupName = input.GroupName
	}

	documentGroup.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repositories.PMODocumentGroup(s.ctx).Where("id = ?", id).Updates(documentGroup)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.DocumentGroupsFind(documentGroup.ID)
}

func (s pmoProjectDocumentService) DocumentGroupsFind(id string) (*models.PMODocumentGroup, core.IError) {
	return repositories.PMODocumentGroup(s.ctx,
		repositories.PMODocumentGroupWithProject(),
		repositories.PMODocumentGroupWithDocumentItems(),
	).FindOne("id = ?", id)
}

func (s pmoProjectDocumentService) DocumentGroupsPagination(pageOptions *core.PageOptions, options *dto.PMODocumentGroupPaginationOptions) (*repository.Pagination[models.PMODocumentGroup], core.IError) {
	return repositories.PMODocumentGroup(s.ctx,
		repositories.PMODocumentGroupOrderBy(pageOptions),
		repositories.PMODocumentGroupWithProject(),
		repositories.PMODocumentGroupWithDocumentItems(),
		repositories.PMODocumentGroupWithSearch(pageOptions.Q),
		repositories.PMODocumentGroupByProjectID(utils.ToNonPointer(options.ProjectID)),
		repositories.PMODocumentGroupByTabKey(utils.ToNonPointer(options.TabKey)),
	).Pagination(pageOptions)
}

func (s pmoProjectDocumentService) DocumentGroupsDelete(id string) core.IError {
	_, ierr := s.DocumentGroupsFind(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMODocumentGroup(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

// PMO Document Item methods implementation
func (s pmoProjectDocumentService) DocumentItemsCreate(input *dto.PMODocumentItemCreatePayload) (*models.PMODocumentItem, core.IError) {
	documentItem := &models.PMODocumentItem{
		BaseModel:     models.NewBaseModel(),
		ProjectID:     input.ProjectID,
		TabKey:        models.PMOTabKey(input.TabKey),
		GroupID:       input.GroupID,
		Name:          input.Name,
		SharepointURL: input.SharepointURL,
		Date:          input.Date,
		Type:          models.PMODocType(input.Type),
		FileID:        input.FileID,
		CreatedByID:   utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID:   utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.PMODocumentItem(s.ctx).Create(documentItem)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create version record
	version := &models.PMODocumentItemVersion{
		PMODocumentItem: *documentItem,
		OriginalID:      documentItem.ID,
	}

	ierr = repositories.PMODocumentItemVersion(s.ctx).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.DocumentItemsFind(documentItem.ID)
}

func (s pmoProjectDocumentService) DocumentItemsUpdate(id string, input *dto.PMODocumentItemUpdatePayload) (*models.PMODocumentItem, core.IError) {
	documentItem, ierr := s.DocumentItemsFind(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Track if file_id or sharepoint_url changes to determine if version should be created
	shouldCreateVersion := false
	originalFileID := documentItem.FileID
	originalSharepointURL := documentItem.SharepointURL

	if input.Name != "" {
		documentItem.Name = input.Name
	}
	if input.SharepointURL != "" {
		if originalSharepointURL != input.SharepointURL {
			shouldCreateVersion = true
		}
		documentItem.SharepointURL = input.SharepointURL
	}
	if input.Date != nil {
		documentItem.Date = input.Date
	}
	if input.Type != "" {
		documentItem.Type = models.PMODocType(input.Type)
	}
	if input.FileID != nil {
		if utils.ToNonPointer(originalFileID) != utils.ToNonPointer(input.FileID) {
			shouldCreateVersion = true
		}
		documentItem.FileID = input.FileID
	}

	documentItem.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repositories.PMODocumentItem(s.ctx).Where("id = ?", id).Updates(documentItem)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create version record only if file_id or sharepoint_url changed
	if shouldCreateVersion {
		version := &models.PMODocumentItemVersion{
			PMODocumentItem: *documentItem,
			OriginalID:      documentItem.ID,
		}

		ierr = repositories.PMODocumentItemVersion(s.ctx).Create(version)
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}
	}

	return s.DocumentItemsFind(documentItem.ID)
}

func (s pmoProjectDocumentService) DocumentItemsFind(id string) (*models.PMODocumentItem, core.IError) {
	return repositories.PMODocumentItem(s.ctx,
		repositories.PMODocumentItemWithProject(),
		repositories.PMODocumentItemWithGroup(),
		repositories.PMODocumentItemWithFile(),
	).FindOne("id = ?", id)
}

func (s pmoProjectDocumentService) DocumentItemsPagination(pageOptions *core.PageOptions, options *dto.PMODocumentItemPaginationOptions) (*repository.Pagination[models.PMODocumentItem], core.IError) {
	return repositories.PMODocumentItem(s.ctx,
		repositories.PMODocumentItemOrderBy(pageOptions),
		repositories.PMODocumentItemWithProject(),
		repositories.PMODocumentItemWithGroup(),
		repositories.PMODocumentItemWithFile(),
		repositories.PMODocumentItemWithSearch(pageOptions.Q),
		repositories.PMODocumentItemByProjectID(utils.ToNonPointer(options.ProjectID)),
		repositories.PMODocumentItemByGroupID(utils.ToNonPointer(options.GroupID)),
		repositories.PMODocumentItemByTabKey(utils.ToNonPointer(options.TabKey)),
		repositories.PMODocumentItemByType(utils.ToNonPointer(options.Type)),
	).Pagination(pageOptions)
}

func (s pmoProjectDocumentService) DocumentItemsDelete(id string) core.IError {
	_, ierr := s.DocumentItemsFind(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMODocumentItem(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

// PMO Document Item Version methods implementation
func (s pmoProjectDocumentService) DocumentItemVersionsPagination(documentItemID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMODocumentItemVersion], core.IError) {
	return repositories.PMODocumentItemVersion(s.ctx,
		repositories.PMODocumentItemVersionOrderBy(pageOptions),
		repositories.PMODocumentItemVersionByOriginalID(documentItemID),
	).Pagination(pageOptions)
}

func (s pmoProjectDocumentService) DocumentItemVersionsFind(versionID string) (*models.PMODocumentItemVersion, core.IError) {
	return repositories.PMODocumentItemVersion(s.ctx).FindOne("id = ?", versionID)
}

