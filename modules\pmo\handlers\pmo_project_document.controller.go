package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectDocumentController struct {
}

// PMO Document Groups methods
func (m PMOProjectDocumentController) DocumentGroupsPagination(c core.IHTTPContext) error {
	input := &requests.PMODocumentGroupPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	res, ierr := pmoDocumentSvc.DocumentGroupsPagination(c.GetPageOptions(), &dto.PMODocumentGroupPaginationOptions{
		ProjectID: utils.ToPointer(c.Param("id")),
		TabKey:    input.TabKey,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectDocumentController) DocumentGroupsFind(c core.IHTTPContext) error {
	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	documentGroup, ierr := pmoDocumentSvc.DocumentGroupsFind(c.Param("group_id"))
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, documentGroup)
}

func (m PMOProjectDocumentController) DocumentGroupsCreate(c core.IHTTPContext) error {
	input := &requests.PMODocumentGroupCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	payload := &dto.PMODocumentGroupCreatePayload{
		ProjectID: c.Param("id"),
		TabKey:    utils.ToNonPointer(input.TabKey),
		GroupName: utils.ToNonPointer(input.GroupName),
	}

	documentGroup, err := pmoDocumentSvc.DocumentGroupsCreate(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, documentGroup)
}

func (m PMOProjectDocumentController) DocumentGroupsUpdate(c core.IHTTPContext) error {
	input := &requests.PMODocumentGroupUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	payload := &dto.PMODocumentGroupUpdatePayload{
		TabKey:    utils.ToNonPointer(input.TabKey),
		GroupName: utils.ToNonPointer(input.GroupName),
	}

	documentGroup, err := pmoDocumentSvc.DocumentGroupsUpdate(c.Param("group_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, documentGroup)
}

func (m PMOProjectDocumentController) DocumentGroupsDelete(c core.IHTTPContext) error {
	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	err := pmoDocumentSvc.DocumentGroupsDelete(c.Param("group_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

// PMO Document Items methods
func (m PMOProjectDocumentController) DocumentItemsPagination(c core.IHTTPContext) error {
	input := &requests.PMODocumentItemPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	res, ierr := pmoDocumentSvc.DocumentItemsPagination(c.GetPageOptions(), &dto.PMODocumentItemPaginationOptions{
		ProjectID: utils.ToPointer(c.Param("id")),
		GroupID:   input.GroupID,
		TabKey:    input.TabKey,
		Type:      input.Type,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectDocumentController) DocumentItemsFind(c core.IHTTPContext) error {
	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	documentItem, ierr := pmoDocumentSvc.DocumentItemsFind(c.Param("item_id"))
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, documentItem)
}

func (m PMOProjectDocumentController) DocumentItemsCreate(c core.IHTTPContext) error {
	input := &requests.PMODocumentItemCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	payload := &dto.PMODocumentItemCreatePayload{
		ProjectID:     c.Param("id"),
		TabKey:        utils.ToNonPointer(input.TabKey),
		GroupID:       utils.ToNonPointer(input.GroupID),
		Name:          utils.ToNonPointer(input.Name),
		SharepointURL: utils.ToNonPointer(input.SharepointURL),
		Date:          input.Date,
		Type:          utils.ToNonPointer(input.Type),
		FileID:        input.FileID,
	}

	documentItem, err := pmoDocumentSvc.DocumentItemsCreate(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, documentItem)
}

func (m PMOProjectDocumentController) DocumentItemsUpdate(c core.IHTTPContext) error {
	input := &requests.PMODocumentItemUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	payload := &dto.PMODocumentItemUpdatePayload{
		Name:          utils.ToNonPointer(input.Name),
		SharepointURL: utils.ToNonPointer(input.SharepointURL),
		Date:          input.Date,
		Type:          utils.ToNonPointer(input.Type),
		FileID:        input.FileID,
	}

	documentItem, err := pmoDocumentSvc.DocumentItemsUpdate(c.Param("item_id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, documentItem)
}

func (m PMOProjectDocumentController) DocumentItemsDelete(c core.IHTTPContext) error {
	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	err := pmoDocumentSvc.DocumentItemsDelete(c.Param("item_id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

// PMO Document Item Version methods
func (m PMOProjectDocumentController) DocumentItemVersionsPagination(c core.IHTTPContext) error {
	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	res, ierr := pmoDocumentSvc.DocumentItemVersionsPagination(c.Param("item_id"), c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectDocumentController) DocumentItemVersionsFind(c core.IHTTPContext) error {
	pmoDocumentSvc := services.NewPMOProjectDocumentService(c)
	version, ierr := pmoDocumentSvc.DocumentItemVersionsFind(c.Param("version_id"))
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, version)
}
