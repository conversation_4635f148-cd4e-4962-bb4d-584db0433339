package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Contract Info Version Repository
var PMOContractInfoVersion = repository.Make[models.PMOContractInfoVersion]()

func PMOContractInfoVersionByContractInfoID(contractInfoID string) repository.Option[models.PMOContractInfoVersion] {
	return func(c repository.IRepository[models.PMOContractInfoVersion]) {
		if contractInfoID != "" {
			c.Where("contract_info_id = ?", contractInfoID)
		}
	}
}
